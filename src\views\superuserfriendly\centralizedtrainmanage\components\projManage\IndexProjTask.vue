<template>
  <div class="proj-task-container">
    <!-- 此页面，日期项目， wpid 为 1604 -->
    <!--   项目任务 ||-->

    <!-- 评估分析 -->
    <AssessAnalyseDrawer ref="assessAnalyseRef" />
    <!-- 签到统计 -->
    <SignStatDrawer ref="signStatDrawerRef" />
    <!--    添加任务弹框-->
    <AddTaskDirDrawer
      ref="addTaskDirRef"
      module-type="project"
      @fetch-data="resetData"
    />

    <!-- 编辑用 -->
    <AddTaskCourse ref="addCourseRef" />
    <AddTaskExam ref="addExamRef" />

    <!-- 任务详情 -->
    <TaskDetail ref="taskDetailRef" />

    <!-- 用户的操作 -->
    <vab-query-form>
      <vab-query-form-left-panel :span="24">
        <el-button type="primary" @click="addDirFc()">
          {{ $t('直接添加') }}
        </el-button>
        <el-button type="primary" @click="pubFc(1)">
          {{ $t('发布') }}
        </el-button>
        <el-button type="primary" @click="pubFc(0)">
          {{ $t('取消发布') }}
        </el-button>
        <el-button type="primary" @click="delFc()">
          {{ $t('删除') }}
        </el-button>
        <el-checkbox
          v-model="taskType"
          :false-label="0"
          :label="$t('显示删除的任务')"
          :true-label="1"
          @change="taskTypeSwitch"
        />
      </vab-query-form-left-panel>
    </vab-query-form>
    <!--  表格 -->
    <Table ref="tableRef" :options="tableOptions">
      <!-- 任务名称列插槽 -->
      <template #taskName="{ row }">
        <ResourcesTitle
          :is-btn="true"
          :title="row.taskName"
          :disabled="row.isDel == 1"
          @to-detail="showDetail(row)"
        >
          <template v-if="row.isDel == 1" #tag>
            <el-tag effect="plain" type="danger">
              {{ $t('已删除') }}
            </el-tag>
          </template>
        </ResourcesTitle>
      </template>
      <template #createType="{ row }">
        {{ row.createType === 1 ? '直接创建' : '引用创建' }}
      </template>
      <template #taskType="{ row }">
        {{ taskTypeMap[row.taskType] }}
      </template>

      <!-- 操作列插槽 -->
      <template #operateBtn="{ row }">
        <div class="tabBtn">
          <TabButton :btns="btnsHandler(row)" :row="row" />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
  import { $t } from '@/i18n'
  import AddTaskDirDrawer from './taskCompoents/AddTaskDirDrawer.vue'
  import SignStatDrawer from '@/allPublicComponents/projManage/appComponents/SignStatDrawer.vue'
  import AssessAnalyseDrawer from '@/allPublicComponents/projManage/appComponents/AssessAnalyseDrawer.vue'
  // 课程
  import AddTaskCourse from './taskCompoents/addTaskComponents/AddTaskCourse.vue'
  // 考试
  import AddTaskExam from './taskCompoents/addTaskComponents/AddTaskExam.vue'

  import {
    tableTelmp,
    tableTelmpWidth,
  } from '~/src/_commonTemplates/constants/tableTelmp'
  import TableConfig from '~/src/_commonTemplates/table/tableConfig'
  import Table from '~/src/_commonTemplates/table/Table.vue'
  import {
    list_ProjectTaskRest,
    publish_ProjectTaskRest,
    remove_ProjectTaskRest,
  } from '~/src/api/projectapi/ProjectTaskRest'
  // 在父组件中引用
  import TaskDetail from './taskCompoents/taskDetail.vue'

  // 设置高亮菜单
  const route: any = useRoute()

  const $baseMessage: any = inject('$baseMessage')
  const $baseConfirm: any = inject('$baseConfirm')

  /**
   *
   * 课程任务
   */
  const assessAnalyseRef = ref<any>(null)
  const signStatDrawerRef = ref<any>(null)
  const tableRef = ref<any>(null)

  const state = reactive<{
    addTaskDirRef: any

    taskType: any
    addCourseRef: any
    addExamRef: any

    taskDetailRef: any
  }>({
    // 直接添加的弹框
    addTaskDirRef: ref(),

    taskType: 0,
    addCourseRef: ref(),
    addExamRef: ref(),

    taskDetailRef: ref(),
  })
  const {
    addTaskDirRef,

    taskType,
    addCourseRef,
    addExamRef,

    taskDetailRef,
  } = toRefs(state)

  /**
   * 操作栏按钮
   */
  const isChooseFc = () => {
    const choosedRows = tableRef.value?.outPutRowsData() || []
    if (choosedRows.length === 0) {
      $baseMessage(
        $t('未选中任何内容，请勾选后重试'),
        'error',
        'vab-hey-message-error'
      )
      return []
    }
    return choosedRows
  }
  // 直接添加 引用添加
  const addDirFc = () => {
    addTaskDirRef.value.show()
  }

  const pubFc = (num: number) => {
    const choosedRows = isChooseFc()
    if (choosedRows.length === 0) return
    const msg = `只能${
      num === 0 ? '取消发布已' : '发布未'
    }发布的、未删除的任务，确定要${num === 0 ? '取消' : ''}发布所选选项吗？`
    // 使用vNode方式
    $baseConfirm(msg, null, async () => {
      const ids: any[] = choosedRows.map((item: any) => item.id)
      const { message, code } = await publish_ProjectTaskRest({
        ids,
        isPublish: num,
      })
      if (code === 0) {
        $baseMessage(message, 'success', 'vab-hey-message-success')
      } else {
        $baseMessage(message, 'error', 'vab-hey-message-error')
      }
      await tableRef.value.fetchData()
    })
  }

  /**
   *
   * 删除
   */
  const delFc = async () => {
    const choosedRows = isChooseFc()
    if (choosedRows.length === 0) return
    const ids: string = choosedRows.map((item: any) => item.id).join(',')
    $baseConfirm($t('是否确认删除？'), null, async () => {
      const { message, code } = await remove_ProjectTaskRest({ ids })
      if (code === 0) {
        $baseMessage(message, 'success', 'vab-hey-message-success')
      } else {
        $baseMessage(message, 'error', 'vab-hey-message-error')
      }
      // 更新数据
      await tableRef.value.fetchData()
    })
  }

  const taskTypeMap = ref({
    course: '课程',
    exam: '考试',
    theoryExam: '线下考试',
  })

  // 表格配置
  const tableOptions = ref<TableConfig>()
  tableOptions.value = {
    api: {
      listApi: list_ProjectTaskRest,
      apiExpand: {
        projectId: route.query.projectId,
        type: state.taskType,
      },
      exportKey: '',
    },
    btnGroup: [],
    tableFirstColumn: 'selection',
    tableColumns: [
      {
        title: $t('任务名称'),
        propName: 'taskName',
        width: tableTelmpWidth.name,
        fixed: 'left',
        slotName: 'taskName',
      },
      {
        title: $t('创建方式'),
        propName: 'createType',
        width: tableTelmpWidth.defult,
        slotName: 'createType',
      },
      {
        title: $t('资源状态'),
        propName: 'isPublish',
        width: tableTelmpWidth.defult,
        formatter: tableTelmp.ispublish,
      },
      {
        title: $t('类型'),
        propName: 'taskType',
        width: tableTelmpWidth.categoryName,
        slotName: 'taskType',
      },
      {
        title: $t('开始时间'),
        propName: 'startTime',
        width: tableTelmpWidth.time,
        formatter: tableTelmp.time,
      },
      {
        title: $t('结束时间'),
        propName: 'endTime',
        width: tableTelmpWidth.time,
        formatter: tableTelmp.time,
      },
      {
        title: $t('发布人'),
        propName: 'publishName',
        width: tableTelmpWidth.person,
      },
      {
        title: $t('操作'),
        propName: 'operateBtn',
        width: tableTelmpWidth.maxoperate,
        align: 'center',
        fixed: 'right',
        slotName: 'operateBtn',
      },
    ],
  }

  const router: any = useRouter()
  const btnsHandler = (row: any) => {
    let btns = []
    const defBtn = [
      {
        name: '编辑',
        disabled: row.isDel,
        fc: () => {
          toNext(row, 0)
        },
      },
      {
        name: '核心能力',
        disabled: row.isDel,
        fc: () => {
          toNext(row, 1)
        },
      },
    ]
    const courseBtn = [
      {
        name: '课程编辑',
        disabled: row.isDel,
        fc: () => {
          toNext(row, 7)
        },
      },
      {
        name: '课件管理',
        disabled: row.isDel,
        fc: () => {
          toNext(row, 8)
        },
      },
      // 暂时先不做讲师管理，先注释
      // {
      //   name: '讲师',
      //   disabled: row.isDel,
      //   fc: () => {
      //     toNext(row, 6)
      //   },
      // },
    ]
    const examBtn = [
      {
        name: '考试编辑',
        disabled: row.isDel,
        fc: () => {
          toNext(row, 7)
        },
      },
      {
        name: '题目管理',
        disabled: row.isDel,
        fc: () => {
          toNext(row, 8)
        },
      },
      {
        name: '改卷',
        disabled: row.isDel,
        fc: () => {
          toNext(row, 6)
        },
      },
      {
        name: '成绩',
        disabled: row.isDel,
        fc: () => {
          toNext(row, 6)
        },
      },
    ]

    const theoryExamBtn = [
      {
        name: '成绩管理',
        disabled: row.isDel,
        fc: () => {
          toNext(row, 9)
        },
      },
    ]

    switch (row.taskType) {
      case 'course':
        btns = defBtn.concat(courseBtn)
        break
      case 'exam':
        btns = defBtn.concat(examBtn)
        break

      case 'theoryExam':
        btns = defBtn.concat(theoryExamBtn)
        break
    }
    return btns
  }
  const toNext = (row: any, num: number) => {
    switch (String(num)) {
      case '0':
        addTaskDirRef.value.show(row) //编辑
        break
      case '1':
        router.push({ name: 'CentralizedTaskCore', query: { id: row.id } }) //核心能力
        break
      case '7':
        addCourseRef.value.show(row)
        break
      case '8':
        router.push({
          name: 'CourseCWmanage',
          query: {
            id: row.taskContent,
            title: row.taskName,
            moduleType: 'course',
          },
        })
        break
      // 线下考试成绩管理
      case '9':
        router.push({
          name: 'TheoryExamScoreManage',
          query: {
            id: row.id,
            projectId: row.proId,
          },
        })
        break
    }
  }

  const taskTypeSwitch = () => {
    tableRef?.value.updateScreenMessage({ type: state.taskType })
  }

  const resetData = () => {
    tableRef?.value.resetData()
  }

  const initTask = () => {
    resetData()
  }
  const showDetail = (row: any) => {
    if (!row) return
    // console.log(row)
    taskDetailRef?.value.show(row.id)
  }
  const tab_onActivated: any = inject('tab_onActivated')
  if (tab_onActivated) {
    // navtabs组件里，模拟的active周期
    tab_onActivated(() => {
      nextTick(() => {
        initTask()
        addExamRef.value.show()
      })
    }, 'CentralizedTrainTaskDate')
  } else {
    // 兼容模块，自身的active周期
    onActivated(() => {
      nextTick(() => {
        initTask()
      })
    })
  }
</script>
<style lang="scss" scoped>
  .proj-task-container {
    .teble-desc {
      display: flex;
      justify-content: flex-start;
    }
    .defectTips {
      color: #f56c6c;
    }
  }
  .other-tag {
    height: fit-content;
    padding: 1px;
    margin-left: 4px !important;
    font-size: 12px;

    &:nth-of-type(1) {
      margin-left: 8px;
    }
  }
  .publish_hover {
    cursor: pointer;
  }

  .publish_hover_tooltip {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .tabBtn {
    display: flex;
    align-items: center;
  }
</style>
