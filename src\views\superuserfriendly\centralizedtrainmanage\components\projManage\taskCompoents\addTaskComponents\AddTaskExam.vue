<template>
  <div class="add-exam-container">
    <!-- 添加考试 || 考试编辑 -->
    <el-drawer
      v-model="drawerVisible"
      :before-close="() => beforeClose(onBack, [oldValue, form])"
      destroy-on-close
      modal-class="ui-drawer"
      :size="DRAWER_SIZE.LARGE"
      @close="onBack"
    >
      <!-- 阅卷人弹框 -->
      <UserDialog
        ref="userDialogRef"
        destroy-on-close
        is-check
        :title="$t('阅卷人')"
        :type="1"
        @choose-item="chooseReadFc"
      />
      <!--引入试卷 -->
      <TestDepotDialog
        v-if="!isedit && drawerVisible"
        ref="getExamDialogRef"
        @get-row="infoFc"
      />
      <!-- 选择题组,数据来源：资源库的考题库 -->
      <ExamDepotDialog
        v-if="!isedit && drawerVisible"
        ref="chooseItemsDialogRef"
        @get-rows="libItems"
      />
      <!-- 选择组卷 -->
      <TestpaperSchemeDialog
        ref="testpaperSchemeDialogRef"
        :is-facility-value="form.schemaMode"
        @select="selectSchema"
      />
      <template #header>
        <h3>
          {{ $t(state.id ? '编辑考试' : '添加考试') }}
        </h3>
      </template>
      <AnChor
        :titles="[
          { id: 'examArrangement', name: '考试安排' },
          { id: 'testPaperSetting', name: '试卷设置' },
          { id: 'examRule', name: '考试规则' },
          { id: 'correctPaperSetting', name: '改卷设置' },
        ]"
      >
        <el-form
          ref="formRef"
          label-position="right"
          label-width="auto"
          :model="form"
          :rules="rules"
        >
          <!-- 考试安排 -->
          <AnchorItem id="examArrangement">
            <el-form-item :label="$t('考试名称：')" prop="examName">
              <el-input
                v-model="form.examName"
                class="fix_width_input"
                clearable
                maxlength="80"
                show-word-limit
              />
            </el-form-item>
            <el-form-item :label="$t('简介：')" prop="description">
              <!-- 【29914】autosize旧值，:autosize="{ minRows: 3, maxRows: 5 }" -->
              <!-- :rows="5"，rows的值根据maxlength设置，大于200设置为5值，小于等于200设置为3值，未设置时默认值3 -->
              <el-input
                v-model="form.description"
                :rows="5"
                maxlength="500"
                show-word-limit
                type="textarea"
              />
            </el-form-item>
            <el-form-item
              class="testtime_formitem"
              :label="$t('考试时长：')"
              prop="examTimeCount"
            >
              <el-input-number v-model.number="form.examTimeCount" />
              <div class="input-number__unit">{{ $t('分钟') }}</div>
            </el-form-item>
          </AnchorItem>

          <!-- 试卷设置 -->
          <AnchorItem id="testPaperSetting">
            <!-- 编辑页面没有这个选项 -->
            <div>
              <el-form-item :label="$t('题目来源：')" prop="sourceType">
                <el-radio-group
                  v-model="form.sourceType"
                  :disabled="state.id"
                  @change="changeType"
                >
                  <el-radio label="ExcelSource">{{ $t('导入试卷') }}</el-radio>
                  <el-radio label="EntenSource">
                    {{ $t('引入试卷') }}
                    <el-tooltip placement="top">
                      <template #content>
                        {{ $t('引入试卷仅复制试卷，不保持题目同步') }}
                      </template>
                      <vab-icon class="question_span" icon="question-fill" />
                    </el-tooltip>
                  </el-radio>
                  <el-radio label="LibSource">
                    {{ $t('题库组卷') }}
                  </el-radio>
                </el-radio-group>
                <span
                  v-if="form.sourceType === 'LibSource'"
                  class="text-tip"
                  style="margin-left: 8px"
                >
                  {{ $t('选择题库组卷后预览试卷将被禁用') }}
                </span>
              </el-form-item>
            </div>
            <!-- 试卷方式三选一 -->
            <!-- 右侧 -->
            <!-- 1.导入试卷excel -->
            <div v-show="form.sourceType === 'ExcelSource' && !state.id">
              <el-form-item
                ref="importItemRef"
                :label="$t('导入试卷：')"
                prop="excelPath"
              >
                <!-- 引用上传附件组件 -->
                <!-- 去除xlsx格式 -->
                <cs-upload
                  ref="uploadItemRef"
                  :disabled="!!isedit"
                  :download-fc="examTemplate"
                  :file-ref="uploadExcelExamRef"
                  :file-type="FILE_TYPE_FC('File_Type_1', 'File_Type_Str')"
                  :has-template="true"
                  :limit-num="1"
                  :tip-text="FILE_TYPE_FC('File_Type_1', 'Tip_Text')"
                  :upload-fc="uploadFile"
                  @remove="handleRemove"
                  @upload-item="examUploadFile"
                />
              </el-form-item>
            </div>

            <!-- 第二种 引入试卷 -->
            <div v-show="form.sourceType === 'EntenSource' && !isedit">
              <el-form-item :label="$t('试卷：')" prop="entenbId">
                <DelButton
                  v-model:id="form.entenbId"
                  v-model:text="form.testPaperName"
                  @click="
                    () => {
                      state.getExamDialogRef.dialogVisible = true
                    }
                  "
                />
              </el-form-item>
            </div>
            <!-- 编辑时候的 -->
            <!--          type2-->
            <div v-if="isedit && form.sourceType === 'EntenSource'">
              <el-button disabled link type="primary">
                <el-form-item :label="$t('试卷：')">
                  {{ form.testPaperName }}
                </el-form-item>
              </el-button>
            </div>

            <!-- type3 -->
            <!-- 编辑且存在答题记录 -->
            <div v-if="isedit && form.sourceType === 'LibSource'">
              <el-form-item :label="$t('选择题组：')">
                <el-button disabled link type="primary">
                  {{ form.examLibId }}
                </el-button>
              </el-form-item>
              <el-tag type="danger">
                {{ $t('考试已存在答题记录，不允许修改题库及组卷方案相关内容') }}
              </el-tag>
            </div>
            <!-- 第三种 题库组卷 -->
            <div v-show="form.sourceType === 'LibSource' && !isedit">
              <!-- 题组选择 -->
              <el-form-item :label="$t('选择题组：')" prop="examLibId">
                <el-button
                  link
                  type="primary"
                  @click="
                    () => {
                      state.chooseItemsDialogRef.show()
                    }
                  "
                >
                  {{ form.examLibId || '请选择' }}
                </el-button>
                <el-button
                  v-show="form.examLibId"
                  circle
                  :icon="Close"
                  size="small"
                  @click="empty"
                />
              </el-form-item>

              <!-- 组卷方案选择 -->
              <el-form-item :label="$t('组卷方案：')" prop="schemaId">
                <el-button link type="primary" @click="showSchemeDialog">
                  {{ schemaValue ? schemaValue.schemaName : $t('请选择') }}
                </el-button>
                <el-button
                  v-show="schemaValue"
                  circle
                  :icon="Close"
                  size="small"
                  @click="schemaValue = ''"
                />
              </el-form-item>
            </div>

            <!-- 分数 时长 -->
            <div class="score_item">
              <el-form-item
                class="testtoal_formitem"
                :label="$t('总分：')"
                prop="totalScore"
              >
                <el-input-number v-model="form.totalScore" disabled />
              </el-form-item>
              <el-form-item
                class="passpoints_formitem"
                :label="$t('及格分：')"
                prop="passScore"
              >
                <el-input-number
                  v-model.number="form.passScore"
                  :disabled="isedit"
                  @input="changeInput"
                />
                <!-- 百分百显示 -->
                <div
                  v-if="
                    !isNaN(form.passScore) &&
                    (form.passScore / form.totalScore) * 100 <= 100 &&
                    form.passScore
                  "
                  class="input-number__unit"
                >
                  {{
                    percent ||
                    Math.round((form.passScore / form.totalScore) * 100)
                  }}%
                </div>
              </el-form-item>
            </div>
            <div>
              <el-form-item
                class="devicetype_formitem"
                :label="$t('积分：')"
                prop="sysSpaceList"
                required
              >
                <div>{{ $t('设置学员得分区间所能获取的积分') }}</div>
                <el-table
                  border
                  style="width: 560px"
                  :data="form?.sysSpaceList || []"
                  tooltip-effect="light"
                >
                  <el-table-column
                    align="center"
                    :label="$t('分数段')"
                    prop="sysSpaceName"
                    width="300"
                  >
                    <template #default="{ row, $index }">
                      <div v-if="$index == 0">
                        <el-input-number
                          v-model="row.min"
                          :maxlength="80"
                          style="width: 80px"
                          :controls="false"
                          :min="0"
                          :max="99999"
                          :precision="0"
                          clearable
                        />
                        <span style="display: inline-block; padding: 0px 5px">
                          {{ $t('到') }}
                        </span>
                        <el-input-number
                          v-model="row.max"
                          :maxlength="80"
                          style="width: 80px"
                          :controls="false"
                          :min="0"
                          :max="99999"
                          :precision="0"
                          clearable
                        />
                      </div>
                      <span v-else>
                        {{ row.sysSpaceName }}
                      </span>
                    </template>
                  </el-table-column>

                  <el-table-column
                    align="center"
                    :label="$t('积分')"
                    prop="fortune"
                    width="160"
                  >
                    <template #default="{ row, $index }">
                      <el-input-number
                        v-if="$index == 0"
                        v-model="row.fortune"
                        :maxlength="80"
                        style="width: 80px"
                        :controls="false"
                        :min="0"
                        :max="99999"
                        :precision="0"
                        clearable
                      />
                      <span v-else>{{ row.fortune }}</span>
                    </template>
                  </el-table-column>

                  <el-table-column
                    align="center"
                    :label="$t('操作')"
                    prop="courseWareNum"
                    width="100"
                  >
                    <template #default="{ row, $index }">
                      <el-button
                        v-if="$index == 0"
                        link
                        type="primary"
                        @click="addSysSpace(row)"
                      >
                        {{ $t('添加') }}
                      </el-button>
                      <el-button
                        v-else
                        link
                        type="danger"
                        @click="deleteSysSpace($index)"
                      >
                        {{ $t('删除') }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </div>
          </AnchorItem>

          <!-- 考试规则 -->
          <AnchorItem id="examRule">
            <!-- 重考保留格力逻辑 -->
            <el-form-item label="及格后重考：" prop="isReExam">
              <el-switch
                v-model="form.isReExam"
                :active-value="1"
                :inactive-value="0"
                @change="reExamChangeFc"
              />
            </el-form-item>
            <el-form-item
              class="reExamCount_formitem"
              :label="$t('重考次数：')"
              prop="reExamCount"
            >
              <el-input-number
                v-model="form.reExamCount"
                :precision="0"
                @change="
                  (val) => {
                    if (!val) state.form.isReExam = 0
                  }
                "
              />
            </el-form-item>

            <el-form-item :label="$t('查看答案：')" prop="isViewAnswer">
              <el-radio-group v-model="form.isViewAnswer">
                <el-radio :label="0">{{ $t('不允许查看答案') }}</el-radio>
                <el-radio :label="1">
                  {{ $t('允许查看答案（考试交卷后）') }}
                </el-radio>
                <el-radio :label="2">
                  {{ $t('允许查看答案（考试结束后）') }}
                </el-radio>
              </el-radio-group>
              <el-tooltip placement="top">
                <template #content>
                  <div class="tips_cls">
                    {{ $t('考试时间截止后+考试时长才可查看答案。') }}
                  </div>
                  <div class="tips_cls">
                    {{
                      $t(
                        '例：考试截止时间为14:00，考试时长2小时，则16:00可查看考试答案'
                      )
                    }}
                  </div>
                </template>
                <vab-icon class="question_span" icon="question-fill" />
              </el-tooltip>
            </el-form-item>
          </AnchorItem>
          <!-- 改卷设置 -->
          <AnchorItem id="correctPaperSetting">
            <!-- 选择阅卷人的弹窗 -->
            <el-form-item :label="$t('阅卷人：')">
              <el-button
                class="text_wrapper"
                link
                type="primary"
                @click="userDialogRef.show()"
              >
                {{
                  form.reviewerList?.length > 0
                    ? form.reviewerList
                        .map((reviewer) => reviewer.reviewerName)
                        .join('，')
                    : $t('请选择')
                }}
              </el-button>
              <el-button
                v-show="form.reviewerList?.length > 0"
                circle
                :icon="Close"
                size="small"
                @click="() => (form.reviewerList = [])"
              />
            </el-form-item>
          </AnchorItem>
        </el-form>
      </AnChor>
      <template #footer>
        <el-button @click="beforeClose(onBack, [oldValue, form])">
          {{ $t('取消') }}
        </el-button>
        <el-button type="primary" @click="onSave">
          {{ $t('确定') }}
        </el-button>
      </template>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  export default defineComponent({ name: 'AddTaskExam' })
</script>
<script lang="ts" setup>
  import { cloneDeep } from 'lodash'
  import { $t } from '@/i18n'
  import { Close } from '@element-plus/icons-vue'
  import { uploadFile } from '~/src/api/fileapi/FileRest'
  import { beforeClose } from '@/utils'
  import { examTemplate } from '@/api/fileapi/importTemplate'

  import { saveTaskContent } from '@/api/projectapi/ProjectTaskRest'
  import { exam_checkExcel, exam_create } from '~/src/api/examapi/ExamRest'
  import { ExamReviewerDTO } from '~/src/api/examapi/typings'

  // 存储原始数据
  const oldValue = ref<any>()
  const userDialogRef = ref()
  const drawerVisible = ref<boolean>(false)
  const uploadExcelExamRef: any = ref() // 考试附件上传ref

  const $baseLoading: any = inject('$baseLoading')
  const $baseMessage: any = inject('$baseMessage')
  const rules = ref<any>({
    examName: [
      { required: true, message: $t('请输入考试名称'), trigger: 'change' },
      {
        min: 0,
        max: 80,
        message: $t('长度在0到80个字符'),
        trigger: 'change',
      },
    ],
    passScore: [
      { required: true, message: $t('请填写及格分'), trigger: 'change' },
      { type: 'number', message: $t('及格分为数字'), trigger: 'change' },
      {
        required: true,
        trigger: 'change',
        validator: async (rule: any, val: any, callback: any) => {
          const score = state.form.totalScore
          if (score || score === 0) {
            if (val > score) {
              return callback($t('请输入正确的及格分'))
            }
            if (val < 1) {
              return callback($t('请输入正确的及格分'))
            }
          }
        },
      },
    ],
    examTimeCount: [
      { required: true, message: $t('请输入考试时长'), trigger: 'change' },
      {
        pattern: /^([1-9]\d{0,2}|1000)$/,
        message: $t('请输入1-1000的数字'),
        trigger: 'change',
      },
    ],
    // 导入试卷字段
    excelQuestionFile: [
      {
        required: true,
        trigger: 'change',
        /**
         * @param {*} rule 规则
         * @param {*} value 输入内容
         * @param {*} callback 回调
         */
        validator: async (rule: any, value: any, callback: any) => {
          let flag = true
          if (state.form.sourceType === 1) {
            if (!value) {
              flag = false
            }
          }
          return !flag && !state.id
            ? callback($t('请选择要导入的试卷'))
            : callback()
        },
      },
    ],
    // 引入试卷字段
    testPaperName: [
      {
        required: true,
        trigger: 'change',
        /**
         * @param {*} rule 规则
         * @param {*} value 输入内容
         * @param {*} callback 回调
         */
        validator: async (rule: any, value: any, callback: any) => {
          value = state.form.testPaperId
          let flag = true
          if (state.form.sourceType === 2) {
            if (!value) {
              flag = false
            }
          }
          if (!flag) {
            return callback($t('请选择试卷'))
          }
          return callback()
        },
      },
    ],
    // 题库组卷使用字段
    examLibNames: [
      {
        required: true,
        trigger: 'change',
        /**
         * @param {*} rule 规则
         * @param {*} value 输入内容
         * @param {*} callback 回调
         */
        validator: async (rule: any, value: any, callback: any) => {
          if (state.form.sourceType !== 3 || state.isedit) return
          if (!state.form.examLibIds?.length) {
            return callback($t('请选择题组'))
          }
        },
      },
    ],
    startTimeAndEndTime: [
      { required: true, message: $t('请输入起止时间'), trigger: 'blur' },
      {
        required: true,
        trigger: 'change',
        /**
         * @param {*} rule 规则
         * @param {*} value 输入内容
         * @param {*} callback 回调
         */
        validator: (rule: any, value: any) => {
          if (value) {
            return true
          }
        },
      },
    ],
    schemaValue: [
      { required: true, message: $t('请选择题库组卷'), trigger: 'change' },
    ],
    reExamCount: [
      {
        trigger: 'change',
        /**
         *
         * @param {*} rule 规则
         * @param {*} value 输入内容
         * @param {*} callback 回调
         *
         */
        validator: (rule: any, value: any, callback: any) => {
          const min = state.form.isReExam ? 1 : 0
          if (typeof value !== 'number' || value < min || value > 99) {
            return callback(new Error($t(`请输入${min}-99的数字`)))
          }
          callback()
        },
      },
    ],
    sysSpaceList: [
      {
        required: true,
        trigger: ['blur', 'change'],
        /**
         * @param {*} rule 规则
         * @param {*} value 输入内容
         * @param {*} callback 回调
         */
        validator: (rule: any, value: any, callback: any) => {
          if (!(state?.form?.sysSpaceList?.length > 1)) {
            return callback(new Error($t('请添加积分规则')))
          }
          callback()
        },
      },
    ],
  })

  const getInitForm = () => {
    return {
      id: '',
      examName: '', // 考试名称
      description: '', // 简介
      examTimeCount: 0, // 考试时长
      sourceType: 'ExcelSource', // 题目导入来源：ExcelSource导入试卷、EntenSource引入试卷、LibSource题库组卷
      totalScore: 0, // 总分
      passScore: 0, // 及格分
      isReExam: 0, // 是否允许补考：0不允许、1允许
      reExamCount: 0, // 补考次数
      isViewAnswer: 0, // 是否考试结束查看答案
      reviewerList: [], // 阅卷人
      excelPath: '', // 题目excel文件
      entenbId: '', // 关联的引入试卷
      examLibId: '', // 考题库Id，可选择多个，用逗号隔开
      schemaId: '', // 当为题库组卷时对应的组卷方案Id
      isTrain: 1, // 考试来源 0-考试管理 1-集中培训
      sysSpaceList: [{}], // 积分规则
    }
  }
  const state = reactive<{
    [key: string]: any
  }>({
    formRef: ref(),
    percent: 0,
    id: '', //编辑 的考试id
    chooseItemsDialogRef: ref(),
    getExamDialogRef: ref(),
    isedit: false, //添加还是编辑
    schemaValue: '', //组卷方案id
    getExamRow: '', //引入试卷的数据
    form: getInitForm(),
    reviewerData: {},
    libList: [], // 用于业务数据处理矩阵题组模式
  })
  const {
    isedit,
    formRef,
    form,
    schemaValue,
    percent,
    chooseItemsDialogRef,
    getExamDialogRef,
  } = toRefs(state)
  /**
   * 去重
   */
  function unique(arr: any[]) {
    for (let i = 0; i < arr.length; i++) {
      for (let j = i + 1; j < arr.length; j++) {
        if (arr[i].categoryId == arr[j].categoryId) {
          arr.splice(j, 1)
          j--
        }
      }
    }
    return arr
  }

  // 阅卷人选择
  const chooseReadFc = (ctx: any) => {
    // 用于输入框显示
    console.log(ctx)
    const examReviewers: ExamReviewerDTO[] = []
    // 取出数据
    ctx.map((item: any) => {
      examReviewers.push({
        id: '',
        reviewerId: item.id,
        reviewerName: item.fullName,
      })
    })

    state.form.reviewerList = unique([
      ...state.form.reviewerList,
      ...examReviewers,
    ])
    console.log(state.form.reviewerList)
  }

  // 获取选择试卷的行的内容
  const infoFc = (ctx: any) => {
    if (!ctx) return
    state.getExamRow = ctx
    state.form.testPaperName = ctx.testPaperName
    state.form.totalScore = ctx.totalScore
    state.form.passScore = Math.round(ctx.totalScore * 0.6)
    state.percent = 60
    // 引入试卷id
    state.form.testPaperId = ctx.id
  }

  // 第三种 选择题组

  const libItems = (ctx: any) => {
    if (!ctx) return

    if (!state?.examLibData.length) {
      state.examLibData = []
    }
    const tempExamLibData = [...state.examLibData]
    tempExamLibData.push(...ctx)
    const set = new Set()
    state.examLibData = tempExamLibData.filter((item) => {
      if (set.has(item.id)) {
        return false
      }
      set.add(item.id)
      return true
    })
    state.form.examLibId = state.examLibData
      .map((item: any) => item.id)
      .join(',')
    state.form.examLibName = state.examLibData
      .map((item: any) => item.libraryName)
      .join(',')
  }

  // 选择组卷方案 获取详情 第三种
  const selectSchema = (scheme: any) => {
    state.schemaData = scheme
    state.form.schemaId = scheme.id
    state.form.schemaName = scheme.schemaName
    state.form.totalScore = scheme.totalScore
    nextTick(() => {
      // 选完方案及格分重置为60
      state.percent = 60
      state.form.passScore = parseInt((state.form.totalScore * 0.6).toString())
    })
  }

  // 文件上传
  const uploadItemRef = ref()
  const examUploadFile = async (data: any) => {
    state.form.excelPath = data.path
    if (data?.path) {
      try {
        const res: any = await exam_checkExcel({ excelFile: data.path })
        state.form.totalScore = res.data.totalScore
        state.form.passScore = parseInt(
          (state.form.totalScore * 0.6).toString()
        )
        state.form.examTimeCount = state.form.examTimeCount
          ? state.form.examTimeCount
          : res.data.totalTimeCount
      } catch (error) {
        state.form.excelPath = ''
        uploadItemRef.value.removeFile()
        return
      }
    }
  }
  const handleRemove = (res: boolean) => {
    if (res) {
      state.form.excelQuestionFile = ''
    }
  }
  // 编辑 考试
  const initShow = async () => {
    // 判断是添加还是编辑
    if (state.id) {
      state.isedit = true
      // await getExam({ id: state.id }).then((res) => {
      //   const { data } = res
      //   state.form = data
      // })

      const nameArr: any = []
      // 阅卷人
      // state.form.examReviewer.map((item: any) => {
      //   nameArr.push(item.categoryName)
      // })
      state.reviewerData.names = nameArr.join(',')

      //不存在答题记录时候去编辑，视为添加。除了题目来源不可编辑，其余的都可以修改
      if (state.form.isExistRecord === 0) {
        state.isedit = false
      }
    } else {
      state.isedit = false
    }
  }

  /**
   * 组卷方案弹窗
   *
   */
  const testpaperSchemeDialogRef = ref()
  const showSchemeDialog = () => {
    if (form.value.examLibIds?.length === 0) {
      $baseMessage('请先选择题组', 'error', 'vab-hey-message-error')
      return
    }
    testpaperSchemeDialogRef.value.show()
  }

  /**
   * 清空选择试卷
   *
   */
  const empty = () => {
    state.getExamRow = []
    // 清空试卷相关信息
    if (state.form.sourceType !== 3) {
      state.form.totalScore = ''
    }
    state.form.testPaperId = ''
    // 清空选择题库相关信息
    state.form.examLibIds = []
    state.schemaValue = ''
    state.form.examLibNames = []
  }

  const changeInput = () => {
    state.percent = 0
    const pattern = /^[+]{0,1}(\d+)$/ // 正整数的正则表达式
    // 不符合正整数时
    if (!pattern.test(state.form.passScore)) {
      // input 框绑定的内容为空
      state.form.passScore = 0
    }
  }

  /**
   * 退出初始化数据
   *
   */
  const onBack = () => {
    drawerVisible.value = false
    state.form = getInitForm()
    state.reviewerData.names = ''
    state.schemaList = []
    state.schemaValue = ''
    state.tagList = ''
    // 清空试卷名称展示
    if (state.getExamRow.testPaperName) {
      state.getExamRow.testPaperName = ''
    }
  }

  type ShowData = {
    taskName?: string
    rowId?: string
    projectTaskId?: string
    isAutoPublish: number
  }

  let projectTaskId = ''

  /**
   * 显示添加抽屉
   */
  const show = async (showData: ShowData) => {
    projectTaskId = showData?.projectTaskId

    drawerVisible.value = true
    state.form.examName = showData?.taskName

    // 设置立即发布状态
    state.form.isPublish = state.id ? 1 : 0
    state.form.isAutoPublish = showData.isAutoPublish || 0
    await initShow()

    oldValue.value = cloneDeep(state.form)
  }
  defineExpose({ show })

  const onSave = async () => {
    // 表单验证
    state.formRef.validate(async (valid: boolean) => {
      if (valid) {
        // 基本数据处理
        if (form.value.reExamCount === null) {
          form.value.reExamCount = 0
        }
        const params = JSON.parse(JSON.stringify(state.form))
        params.sysSpaceList.splice(0, 1)
        const loading = $baseLoading()
        try {
          // 调用 exam_create 接口
          const { data } = await exam_create({
            ...params,
            isTrain: 1, // 考试来源：集中培训
          })

          // 保存任务内容
          const res = await saveTaskContent({
            projectTaskId: projectTaskId, // 任务id
            taskContent: data, // 任务内容id
          })

          $baseMessage(res.message, 'success', 'vab-hey-message-success')
          onBack()
        } catch (error) {
          $baseMessage($t('保存失败'), 'error', 'vab-hey-message-error')
        } finally {
          await loading.close()
        }
      }
    })
  }

  /**
   * 改变题目来源事件
   */
  const changeType = async (e: any) => {
    // 清空相关字段
    if (e === 'ExcelSource') {
      form.value.entenbId = ''
      form.value.examLibId = ''
      form.value.schemaId = ''
    } else if (e === 'EntenSource') {
      form.value.excelPath = ''
      form.value.examLibId = ''
      form.value.schemaId = ''
    } else if (e === 'LibSource') {
      form.value.excelPath = ''
      form.value.entenbId = ''
    }
  }

  const reExamChangeFc = () => {
    if (state.form.isReExam === 0) {
      formRef.value.clearValidate('reExamCount')
    } else {
      //启用时，立即校验下面的重考次数
      formRef.value.validateField(['reExamCount'])
    }
  }

  const addSysSpace = (row) => {
    if (typeof row.fortune != 'number') {
      $baseMessage($t('积分设置不符合规则'), 'error', 'vab-hey-message-error')
      return
    }
    if (
      typeof row.min != 'number' ||
      typeof row.max != 'number' ||
      row.min > row.max
    ) {
      $baseMessage($t('分数段设置不符合规则'), 'error', 'vab-hey-message-error')
      return
    } else if (row.min === row.max) {
      $baseMessage($t('分数段不能相同'), 'error', 'vab-hey-message-error')
      return
    }

    const sysSpaceName = `${row.min}分 ~ ${row.max}分`
    state.form?.sysSpaceList.push({ sysSpaceName, fortune: row.fortune })
    row.min = ''
    row.max = ''
    row.fortune = ''
    state.formRef.clearValidate('sysSpaceList')
  }

  const deleteSysSpace = (index) => {
    state.form?.sysSpaceList.splice(index, 1)
  }
</script>

<style lang="scss" scoped>
  .text-tip-row {
    width: 100%;
    font-size: 12px;
    color: #c0c4cc;
  }

  .add-exam-container {
    .score_item {
      .el-input {
        width: 120px;
      }
    }
    .text_span {
      margin-left: 8px;
    }
    .tips_cls {
      width: 20px;
    }
    .question_span {
      margin-left: 8px;
    }
    .text_wrapper {
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal !important;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    :deep() {
      .is-error-inp .el-input__wrapper {
        box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
      }

      .main-container {
        .slot_container {
          overflow-y: auto;
        }
      }
    }
  }

  .devicetype_formitem {
    :deep() {
      .el-form-item__content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
    }
  }
</style>
