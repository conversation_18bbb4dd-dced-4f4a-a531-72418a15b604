<template>
  <div class="add-task-dir-drawer">
    <!-- 直接添加任务 -->
    <AddTaskCourse ref="addCourseRef" />
    <AddTaskExam ref="addExamRef" />
    <AddTaskSurvey ref="addSurveyRef" />
    <el-drawer
      v-if="dialogVisible"
      v-model="dialogVisible"
      :before-close="close"
      modal-class="ui-drawer"
      :size="DRAWER_SIZE.MEDIUM"
    >
      <template #header>
        <h3>{{ $t('直接添加任务') }}</h3>
      </template>

      <AnChor
        :titles="[{ id: 'taskArrange', name: '任务安排' }]"
        :titles-show="true"
      >
        <el-form
          ref="formRef"
          label-width="auto"
          :model="inputForm"
          :rules="rules"
        >
          <AnchorItem id="taskArrange" padding-top="0px">
            <el-form-item :label="$t('任务类型：')" prop="taskType">
              <el-select
                v-model="inputForm.taskType"
                fit-input-width
                :placeholder="$t('请选择')"
              >
                <el-option
                  v-for="item in state.taskTypeOptions"
                  :key="item.code"
                  :disabled="item.disabled"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('任务名称：')" prop="taskName">
              <el-input
                v-model="inputForm.taskName"
                class="fix_width_input"
                clearable
                maxlength="80"
                :placeholder="$t('请输入')"
                show-word-limit
              />
            </el-form-item>
            <el-form-item :label="$t('任务时间：')" prop="endTime">
              <aTime
                v-model:end="inputForm.endTime"
                v-model:start="inputForm.startTime"
                is-detail
              />
            </el-form-item>
            <p class="form-tip-padding">
              {{ $t('说明：') }}
              <br />
              {{
                $t(
                  '对于当前项目已经参与的人员，如果需要补派任务，请到任务编辑页面进行补派操作。'
                )
              }}
            </p>
          </AnchorItem>
        </el-form>
      </AnChor>

      <template #footer>
        <el-button @click="close">{{ $t('取消') }}</el-button>
        <el-button type="primary" @click="onSave()">
          {{ $t('确定') }}
        </el-button>
      </template>
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
  import { $t } from '@/i18n'
  import { beforeClose } from '@/utils'
  import {
    saveOrUpdate_ProjectTaskRest,
    taskTypeList,
    get_ProjectTaskRest,
    saveTaskContent,
  } from '@/api/projectapi/ProjectTaskRest'
  import AddTaskCourse from './addTaskComponents/AddTaskCourse.vue'
  import AddTaskExam from './addTaskComponents/AddTaskExam.vue'
  import AddTaskSurvey from './addTaskComponents/AddTaskSurvey.vue'

  const route = useRoute()
  const $baseLoading: any = inject('$baseLoading')
  const isEdit = ref<boolean>(false)

  const emit = defineEmits(['fetch-data'])

  // 存储原始数据
  const oldValue = ref<any>()
  const state = reactive<{
    dialogVisible: boolean
    rules: any
    [key: string]: any
  }>({
    dialogVisible: false,
    rules: {
      taskName: [
        { required: true, message: $t('请输入任务名称'), trigger: 'change' },
      ],
      taskType: [
        { required: true, message: $t('请选择任务类型'), trigger: 'change' },
      ],
      endTime: [
        { required: true, message: $t('请选择时间'), trigger: 'change' },
      ],
    },
    phaseOptions: [],
    taskOptions: [],
    formRef: ref(),
  })
  const { dialogVisible, rules, formRef } = toRefs(state)

  type inputType = {
    id: string // id
    proId: string // 项目ID
    taskName: string // 任务名称
    taskTypeName: string // 任务内容名称
    taskContent: string // 任务内容id
    startTime: string // 任务开始时间
    endTime: string // 任务结束时间
    taskType: string // 任务类型
    sort: number // 序号
    isPublish: number // 是否发布
  }

  const inputForm = ref<inputType>({
    id: '', // id
    proId: '', // 项目ID
    taskName: '', // 任务名称
    taskTypeName: '', // 任务内容名称
    taskContent: '', // 任务内容id
    startTime: '', // 任务开始时间
    endTime: '', // 任务结束时间
    taskType: '', // 任务类型
    sort: 0, // 序号
    isPublish: 0, // 是否发布
  })

  const taskId = ref()
  const show = async (showData: any) => {
    taskId.value = showData?.row?.id || showData?.id
    state.dialogVisible = true
    // 获取任务类型
    await getTaskTypeList()

    // 判断是否为编辑场景
    if (taskId.value) {
      const loading = $baseLoading()
      // 编辑场景
      isEdit.value = true
      await get_ProjectTaskRest({ id: taskId.value })
        .then((res) => {
          if (res.code == 0) {
            Object.keys(res.data).forEach((key) => {
              inputForm.value[key] = res.data[key]
            })
            // 保存原始数据用于比较变化
            oldValue.value = JSON.stringify(inputForm.value)
          }
        })
        .finally(() => {
          loading.close()
        })
    } else {
      // 新增场景：重置表单
      resetForm()
      isEdit.value = false
      // 保存原始数据用于比较变化
      oldValue.value = JSON.stringify(inputForm.value)
    }
  }

  // 重置表单
  const resetForm = () => {
    Object.assign(inputForm.value, {
      id: '',
      proId: '',
      taskName: '',
      taskTypeName: '',
      taskContent: '',
      startTime: '',
      endTime: '',
      taskType: '',
      sort: 0,
      isPublish: 0,
    })
  }

  const getTaskTypeList = async () => {
    // 获取任务类型
    const { data } = await taskTypeList()
    state.taskTypeOptions = data
  }

  // 关闭
  const close = () => {
    // 3.比较数据变化，采用不同关闭方式
    if (oldValue.value !== JSON.stringify(inputForm.value)) {
      beforeClose(onBack)
    } else {
      onBack()
    }
  }
  const onBack = () => {
    state.dialogVisible = false
    resetForm()
    taskId.value = ''
    isEdit.value = false
  }

  const addCourseRef = ref<any>('')
  const addExamRef = ref<any>('')
  const addSurveyRef = ref<any>('')
  const addLiveRef = ref<any>('')

  //添加点开对应弹框的ref映射
  const taskTypeMap = ref<any>({
    course: addCourseRef,
    exam: addExamRef,
    live: addLiveRef,
    survey: addSurveyRef,
  })
  const onSave = async () => {
    state.formRef.validate(async (v: boolean) => {
      if (!v) return
      const loading = $baseLoading()

      // 添加项目ID到请求数据
      const requestData = Object.assign({}, inputForm.value, {
        proId: route.query.projectId,
      })

      await saveOrUpdate_ProjectTaskRest(requestData)
        .then(async ({ data }: any) => {
          // 打开对应弹框  传入拿到的任务id 编辑模式下不用跳转到对应的任务内容添加
          if (taskTypeMap.value[inputForm.value.taskType] && !isEdit.value) {
            taskTypeMap.value[inputForm.value.taskType].show({
              projectTaskId: data.id,
              taskName: inputForm.value.taskName,
            })
            onBack()
          } else if (
            inputForm.value.taskType == 'theoryExam' &&
            !isEdit.value
          ) {
            await saveTaskContent({
              projectTaskId: data.id,
              taskContent: '1',
            }).then((res) => {
              if (res.code == 0) {
                onBack()
                emit('fetch-data')
              }
            })
          } else {
            emit('fetch-data')
            //关闭弹框清空数据
            onBack()
          }
        })
        .finally(() => loading.close())
        .catch(() => {})
    })
  }

  defineExpose({
    show,
  })
</script>

<style lang="scss" scoped>
  .form-tip-padding {
    padding: 20px 0 0 0;
  }
  :deep() {
    .main-container {
      height: 100%;
    }
    .slot_container {
      height: 100%;
    }
  }
</style>
